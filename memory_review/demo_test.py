from pymongo import MongoClient
from pymongo import ASCENDING
crawl_mongodb_url = "mongodb://dc-crawler-ro:<EMAIL>:27017/?replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
crawl_mongodb_name = "kc_crawler"
crawl_mongodb_table = "news_flash"
ai_mongodb_url = "mongodb://dc-ai-rw:<EMAIL>:27017/?replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false"
ai_mongodb_name = "content_hub"
ai_mongodb_table = "coin_sentiment"
def get_twitter_data(front_time, current_time):
    # 连接到 MongoDB
    client = MongoClient(crawl_mongodb_url)
    # 选择数据库和集合
    db = client[crawl_mongodb_name]
    collection = db[crawl_mongodb_table]
    query = {"type": "tweets", "insert_time": {"$gt": front_time, "$lte": current_time}}
    results = [r for r in  collection.find(query).sort('created_time', ASCENDING)]
    return results
def get_ai_data(front_time, current_time):
    # 连接到 MongoDB
    client = MongoClient(ai_mongodb_url)
    # 选择数据库和集合
    db = client[ai_mongodb_name]
    collection = db[ai_mongodb_table]
    query = {"publish_time": {"$gt": front_time, "$lte": current_time}}
    results = [r for r in  collection.find(query).sort('publish_time', ASCENDING)]
    return results
print(get_ai_data("2025-07-16 17:10:00", "2025-07-16 17:40:00"))